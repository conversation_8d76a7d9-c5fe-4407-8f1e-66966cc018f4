#!/usr/bin/env python3

'''
MySQL MCP服务器 - FastMCP实现版本,为Model Context Protocol提供MySQL数据库操作功能
支持安全的数据库查询、更新、插入和删除操作,包含性能优化、异步支持、智能缓存等增强功能
@author: liyq
@version: 1.0.0
@date: 2025-08-07
'''

import sys
import json
import os
import re
import time
import uuid
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable, Union, Tuple
import signal
import threading
from collections import defaultdict, deque, OrderedDict
from contextlib import contextmanager

import mysql.connector.pooling
from mysql.connector import Error as MySQLError

# 导入FastMCP相关模块
try:
    from fastmcp import FastMCP, Context
except ImportError:
    print("错误: FastMCP未安装。请使用以下命令安装: pip install fastmcp", file=sys.stderr)
    sys.exit(1)

# 导入常量定义
from constants import MySQLErrorCodes, DefaultConfig, StringConstants


# 时间序列指标点
@dataclass
class MetricPoint:
    timestamp: float
    value: float
    labels: Optional[Dict[str, str]] = None


# 时间序列指标收集器
class TimeSeriesMetrics:
    """时间序列指标收集器，支持数据保留和统计分析"""
    def __init__(self, max_points: int = 1000, retention_seconds: int = 3600):
        self.max_points = max_points
        self.retention_seconds = retention_seconds
        self.points: deque[MetricPoint] = deque(maxlen=max_points)
        self.lock = threading.RLock()
    
    def add_point(self, value: float, labels: Optional[Dict[str, str]] = None) -> None:
        """添加指标点"""
        with self.lock:
            now = time.time()
            # 清理过期数据点
            while self.points and (now - self.points[0].timestamp) > self.retention_seconds:
                self.points.popleft()
            
            self.points.append(MetricPoint(timestamp=now, value=value, labels=labels))
    
    def get_stats(self, since_seconds: int = 300) -> Dict[str, float]:
        """获取最近时间段的统计信息"""
        with self.lock:
            cutoff = time.time() - since_seconds
            recent_points = [p.value for p in self.points if p.timestamp >= cutoff]
            
            if not recent_points:
                return {"count": 0, "avg": 0, "min": 0, "max": 0, "sum": 0}
            
            return {
                "count": len(recent_points),
                "avg": sum(recent_points) / len(recent_points),
                "min": min(recent_points),
                "max": max(recent_points),
                "sum": sum(recent_points),
                "p95": self._percentile(recent_points, 0.95),
                "p99": self._percentile(recent_points, 0.99)
            }
    
    def _percentile(self, values: List[float], p: float) -> float:
        """计算百分位数"""
        if not values:
            return 0.0
        sorted_values = sorted(values)
        index = int(len(sorted_values) * p)
        return sorted_values[min(index, len(sorted_values) - 1)]


# 增强指标管理器
class EnhancedMetricsManager:
    """增强指标管理器，支持告警和系统监控"""
    def __init__(self) -> None:
        self.query_times = TimeSeriesMetrics()
        self.error_counts = TimeSeriesMetrics()
        self.cache_hit_rates = TimeSeriesMetrics()
        self.system_metrics = TimeSeriesMetrics()
        
        self.alert_callbacks: List[Callable] = []
        self.alert_rules = self._setup_default_alert_rules()
        
        # 后台系统指标收集线程
        self.metrics_thread = threading.Thread(target=self._collect_system_metrics, daemon=True)
        self.shutdown_event = threading.Event()
    
    def start_monitoring(self) -> None:
        """启动监控线程"""
        if not self.metrics_thread.is_alive():
            self.metrics_thread.start()
    
    def stop_monitoring(self) -> None:
        """停止监控线程"""
        self.shutdown_event.set()
        if self.metrics_thread.is_alive():
            self.metrics_thread.join(timeout=5)
    
    def record_query_time(self, duration: float, query_type: Optional[str] = None) -> None:
        """记录查询执行时间"""
        labels = {"query_type": query_type} if query_type else None
        self.query_times.add_point(duration, labels)
        
        # 检查慢查询告警
        if duration > 2.0:  # 超过2秒的慢查询
            self._trigger_alert("慢查询", {"duration": duration, "query_type": query_type})
    
    def record_error(self, error_type: str, severity: str = "medium") -> None:
        """记录错误发生"""
        self.error_counts.add_point(1, {"error_type": error_type, "severity": severity})
        
        if severity == "high":
            self._trigger_alert("高严重性错误", {"error_type": error_type})
    
    def record_cache_hit_rate(self, hit_rate: float, cache_type: Optional[str] = None) -> None:
        """记录缓存命中率"""
        labels = {"cache_type": cache_type} if cache_type else None
        self.cache_hit_rates.add_point(hit_rate, labels)
        
        # 检查低命中率告警
        if hit_rate < 0.6:  # 命中率低于60%
            self._trigger_alert("缓存命中率过低", {"hit_rate": hit_rate, "cache_type": cache_type})
    
    def _collect_system_metrics(self) -> None:
        """后台收集系统指标"""
        while not self.shutdown_event.wait(30):  # 每30秒收集一次
            try:
                # 尝试导入psutil，如果不可用则跳过系统监控
                try:
                    import psutil
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory_percent = psutil.virtual_memory().percent
                    
                    self.system_metrics.add_point(cpu_percent, {"metric": "cpu_usage"})
                    self.system_metrics.add_point(memory_percent, {"metric": "memory_usage"})
                    
                    # 检查系统级告警
                    if cpu_percent > 90:
                        self._trigger_alert("CPU使用率过高", {"cpu_percent": cpu_percent})
                    if memory_percent > 95:
                        self._trigger_alert("内存使用率过高", {"memory_percent": memory_percent})
                        
                except ImportError:
                    # psutil不可用，跳过系统监控
                    pass
                    
            except Exception:
                pass  # 不让指标收集影响系统运行
    
    def _setup_default_alert_rules(self) -> Dict[str, Dict]:
        """设置默认告警规则"""
        return {
            "慢查询": {"threshold": 2.0, "window": 300, "count": 5},
            "高错误率": {"threshold": 0.05, "window": 300},
            "低缓存命中率": {"threshold": 0.6, "window": 600}
        }
    
    def add_alert_callback(self, callback: Callable) -> None:
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def _trigger_alert(self, alert_type: str, context: Dict[str, Any]) -> None:
        """触发告警到已注册的回调函数"""
        for callback in self.alert_callbacks:
            try:
                callback(alert_type, context)
            except Exception:
                pass  # 不让告警失败影响系统
    
    def get_comprehensive_metrics(self) -> Dict[str, Any]:
        """获取综合指标报告"""
        return {
            "query_performance": self.query_times.get_stats(),
            "error_statistics": self.error_counts.get_stats(), 
            "cache_performance": self.cache_hit_rates.get_stats(),
            "system_metrics": self.system_metrics.get_stats(),
            "alert_rules": self.alert_rules
        }


# 配置管理
@dataclass
class ConfigSection:
    """基础配置段，带验证功能"""
    def __post_init__(self) -> None:
        self.validate()
    
    def validate(self) -> None:
        """在子类中重写以实现验证"""
        pass


@dataclass
class DatabaseConfig(ConfigSection):
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    user: str = "root"
    password: str = ""
    database: str = ""
    connection_limit: int = 10
    connect_timeout: int = 60
    idle_timeout: int = 60
    ssl_enabled: bool = False
    
    def validate(self) -> None:
        # 数据库名称可以为空，在连接时再验证
        if self.port < 1 or self.port > 65535:
            raise ValueError("端口号无效")
        if self.connection_limit < 1:
            raise ValueError("连接池大小必须为正数")


@dataclass
class SecurityConfig(ConfigSection):
    """安全配置"""
    max_query_length: int = 10000
    allowed_query_types: List[str] = field(default_factory=lambda: ["SELECT", "SHOW", "DESCRIBE"])
    max_result_rows: int = 1000
    query_timeout: int = 30
    rate_limit_max: int = 100
    rate_limit_window: int = 60
    
    def validate(self) -> None:
        if self.max_query_length <= 0:
            raise ValueError("最大查询长度必须为正数")
        if self.max_result_rows <= 0:
            raise ValueError("最大结果行数必须为正数")
        if not self.allowed_query_types:
            raise ValueError("必须指定至少一种允许的查询类型")


@dataclass
class CacheConfig(ConfigSection):
    """缓存配置"""
    schema_cache_size: int = 128
    table_exists_cache_size: int = 64
    index_cache_size: int = 64
    cache_ttl: int = 300
    
    def validate(self) -> None:
        cache_sizes = [self.schema_cache_size, self.table_exists_cache_size, self.index_cache_size]
        if any(size <= 0 for size in cache_sizes):
            raise ValueError("缓存大小必须为正数")
        if self.cache_ttl <= 0:
            raise ValueError("缓存TTL必须为正数")


class ConfigurationManager:
    """集中配置管理器，支持验证和诊断"""
    def __init__(self) -> None:
        self.database = self._load_database_config()
        self.security = self._load_security_config()
        self.cache = self._load_cache_config()
    
    def _load_database_config(self) -> DatabaseConfig:
        """加载和验证数据库配置"""
        return DatabaseConfig(
            host=os.getenv("MYSQL_HOST", "localhost"),
            port=int(os.getenv("MYSQL_PORT", "3306")),
            user=os.getenv("MYSQL_USER", "root"),
            password=os.getenv("MYSQL_PASSWORD", ""),
            database=os.getenv("MYSQL_DATABASE", ""),
            connection_limit=int(os.getenv("MYSQL_CONNECTION_LIMIT", "10")),
            connect_timeout=int(os.getenv("MYSQL_CONNECT_TIMEOUT", "60")),
            idle_timeout=int(os.getenv("MYSQL_IDLE_TIMEOUT", "60")),
            ssl_enabled=os.getenv("MYSQL_SSL", "false").lower() == "true"
        )
    
    def _load_security_config(self) -> SecurityConfig:
        """加载和验证安全配置"""
        allowed_types_str = os.getenv("ALLOWED_QUERY_TYPES", "SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER")
        allowed_types = [t.strip().upper() for t in allowed_types_str.split(",")]
        
        return SecurityConfig(
            max_query_length=int(os.getenv("MAX_QUERY_LENGTH", "10000")),
            allowed_query_types=allowed_types,
            max_result_rows=int(os.getenv("MAX_RESULT_ROWS", "1000")),
            query_timeout=int(os.getenv("QUERY_TIMEOUT", "30")),
            rate_limit_max=int(os.getenv("RATE_LIMIT_MAX", "100")),
            rate_limit_window=int(os.getenv("RATE_LIMIT_WINDOW", "60"))
        )
    
    def _load_cache_config(self) -> CacheConfig:
        """加载和验证缓存配置"""
        return CacheConfig(
            schema_cache_size=int(os.getenv("SCHEMA_CACHE_SIZE", "128")),
            table_exists_cache_size=int(os.getenv("TABLE_EXISTS_CACHE_SIZE", "64")),
            index_cache_size=int(os.getenv("INDEX_CACHE_SIZE", "64")),
            cache_ttl=int(os.getenv("CACHE_TTL", "300"))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """导出配置用于诊断（屏蔽敏感信息）"""
        config_dict = {
            "database": self.database.__dict__.copy(),
            "security": self.security.__dict__.copy(),
            "cache": self.cache.__dict__.copy()
        }
        
        # 屏蔽敏感信息
        if "password" in config_dict["database"]:
            config_dict["database"]["password"] = "***"
        
        return config_dict
    
    def get_summary(self) -> Dict[str, str]:
        """获取配置摘要"""
        return {
            "database_host": self.database.host,
            "database_port": str(self.database.port),
            "connection_limit": str(self.database.connection_limit),
            "max_result_rows": str(self.security.max_result_rows),
            "rate_limit_max": str(self.security.rate_limit_max),
            "schema_cache_size": str(self.cache.schema_cache_size)
        }


@dataclass
class PerformanceMetrics:
    """性能指标收集器"""
    query_count: int = 0
    total_query_time: float = 0.0
    slow_query_count: int = 0
    error_count: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    connection_pool_hits: int = 0
    connection_pool_waits: int = 0
    
    def get_avg_query_time(self) -> float:
        return self.total_query_time / max(self.query_count, 1)
    
    def get_cache_hit_rate(self) -> float:
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / max(total, 1)
    
    def get_error_rate(self) -> float:
        return self.error_count / max(self.query_count, 1)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            StringConstants.FIELD_QUERY_COUNT: self.query_count,
            StringConstants.FIELD_AVG_QUERY_TIME: self.get_avg_query_time(),
            StringConstants.FIELD_SLOW_QUERY_COUNT: self.slow_query_count,
            StringConstants.FIELD_ERROR_COUNT: self.error_count,
            StringConstants.FIELD_ERROR_RATE: self.get_error_rate(),
            StringConstants.FIELD_CACHE_HIT_RATE: self.get_cache_hit_rate(),
            StringConstants.FIELD_CONNECTION_POOL_HITS: self.connection_pool_hits,
            StringConstants.FIELD_CONNECTION_POOL_WAITS: self.connection_pool_waits
        }


# 智能缓存项类
@dataclass
class CacheEntry:
    """缓存项"""
    data: Any
    created_at: float
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    
    def is_expired(self, ttl: int) -> bool:
        return time.time() - self.created_at > ttl
    
    def access(self) -> None:
        self.access_count += 1
        self.last_accessed = time.time()


# 智能缓存管理器
class SmartCache:
    """智能缓存管理器，支持TTL和统计，使用OrderedDict实现O(1) LRU操作"""
    
    def __init__(self, max_size: int, ttl: int = DefaultConfig.CACHE_TTL):
        self.max_size = max_size
        self.ttl = ttl
        from collections import OrderedDict
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.hit_count = 0
        self.miss_count = 0
    
    def get(self, key: str) -> Optional[Any]:
        with self.lock:
            entry = self.cache.get(key)
            if entry is None:
                self.miss_count += 1
                return None
            
            if entry.is_expired(self.ttl):
                del self.cache[key]
                self.miss_count += 1
                return None
            
            # O(1)操作：移动到末尾（最近使用）
            self.cache.move_to_end(key)
            entry.access()
            self.hit_count += 1
            return entry.data
    
    def put(self, key: str, value: Any) -> None:
        with self.lock:
            # 如果键已存在，更新并移动到末尾
            if key in self.cache:
                self.cache[key] = CacheEntry(data=value, created_at=time.time())
                self.cache.move_to_end(key)
                return
            
            # 如果缓存已满，清理最久未访问的项
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            self.cache[key] = CacheEntry(data=value, created_at=time.time())
    
    def clear(self) -> None:
        with self.lock:
            self.cache.clear()
    
    def _evict_lru(self) -> None:
        """O(1)移除最久未访问的缓存项"""
        if self.cache:
            # O(1)操作：移除第一个（最久未使用的）
            self.cache.popitem(last=False)
    
    def get_stats(self) -> Dict[str, Any]:
        with self.lock:
            total = self.hit_count + self.miss_count
            return {
                StringConstants.FIELD_SIZE: len(self.cache),
                StringConstants.FIELD_MAX_SIZE: self.max_size,
                StringConstants.FIELD_HIT_COUNT: self.hit_count,
                StringConstants.FIELD_MISS_COUNT: self.miss_count,
                StringConstants.FIELD_HIT_RATE: self.hit_count / max(total, 1),
                StringConstants.FIELD_TTL: self.ttl
            }


# 增强安全验证器
class EnhancedSecurityValidator:
    """全面的安全验证器，包含SQL注入检测"""
    
    # 更全面的危险模式
    DANGEROUS_PATTERNS = [
        re.compile(r'\b(LOAD_FILE|INTO\s+OUTFILE|INTO\s+DUMPFILE)\b', re.IGNORECASE),
        re.compile(r'\b(SYSTEM|EXEC|SHELL|xp_cmdshell)\b', re.IGNORECASE),
        re.compile(r'\b(UNION\s+SELECT).*(\bFROM\s+INFORMATION_SCHEMA)\b', re.IGNORECASE),
        re.compile(r';\s*(DROP|DELETE|TRUNCATE|ALTER)\b', re.IGNORECASE),
        re.compile(r'\b(BENCHMARK|SLEEP|WAITFOR)\s*\(', re.IGNORECASE),
        re.compile(r'@@(version|datadir|basedir|tmpdir)', re.IGNORECASE),
    ]
    
    # SQL注入模式
    INJECTION_PATTERNS = [
        re.compile(r"(\s|^)(\'|\")\s*(OR|AND)\s*(\d+|\'[^\']*\'|\")[^><=!]*(\s)*[><=!]{1,2}.*", re.IGNORECASE),
        re.compile(r"(\'|\").*(\s|^)(UNION|SELECT|INSERT|DELETE|UPDATE|DROP|CREATE|ALTER)(\s)", re.IGNORECASE),
        re.compile(r"\s*(OR|AND)\s+[\w\'\"]+\s*[><=!]+.*", re.IGNORECASE),
        re.compile(r"(\'\s*OR\s*\'\d+\'\s*=\s*\'\d+)", re.IGNORECASE),
        re.compile(r"(\"\s*OR\s*\"\d+\"\s*=\s*\"\d+)", re.IGNORECASE),
    ]
    
    @classmethod
    def validate_input_comprehensive(cls, input_value: Any, field_name: str, 
                                   validation_level: str = "strict") -> None:
        """多级别全面输入验证"""
        if not isinstance(input_value, (str, int, float, bool, type(None))):
            raise ValueError(f"{field_name}的数据类型无效")
        
        if isinstance(input_value, str):
            cls._validate_string_comprehensive(input_value, field_name, validation_level)
    
    @classmethod
    def _validate_string_comprehensive(cls, value: str, field_name: str, level: str) -> None:
        """增强字符串验证"""
        # 检查控制字符（除了常见的制表符、换行符）
        if any(ord(c) < 32 and c not in ['\t', '\n', '\r'] for c in value):
            raise ValueError(f"{field_name}包含无效控制字符")
        
        # 长度验证
        if len(value) > DefaultConfig.MAX_INPUT_LENGTH:
            raise ValueError(f"{field_name}超过最大长度限制")
        
        # 编码验证
        try:
            value.encode('utf-8')
        except UnicodeEncodeError:
            raise ValueError(f"{field_name}包含无效字符编码")
        
        if level == "strict":
            # 检查危险模式
            if any(pattern.search(value) for pattern in cls.DANGEROUS_PATTERNS):
                raise ValueError(f"{field_name}包含潜在危险内容")
            
            # 检查SQL注入模式
            if any(pattern.search(value) for pattern in cls.INJECTION_PATTERNS):
                raise ValueError(f"{field_name}存在潜在SQL注入尝试")
        elif level == "moderate":
            # 只检查最危险的模式
            critical_patterns = cls.DANGEROUS_PATTERNS[:3]  # 前三个最危险的模式
            if any(pattern.search(value) for pattern in critical_patterns):
                raise ValueError(f"{field_name}包含危险内容")


# 令牌桶速率限制器
class TokenBucketRateLimiter:
    """令牌桶速率限制器，支持突发处理"""
    def __init__(self, capacity: int, refill_rate: float, window: int = 60):
        self.capacity = capacity
        self.tokens: float = capacity
        self.refill_rate = refill_rate  # 每秒令牌数
        self.window = window
        self.last_refill = time.time()
        self.lock = threading.RLock()
    
    def allow_request(self, tokens_requested: int = 1) -> bool:
        """检查是否允许请求"""
        with self.lock:
            now = time.time()
            
            # 根据经过时间补充令牌
            elapsed = now - self.last_refill
            tokens_to_add = elapsed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + tokens_to_add)
            self.last_refill = now
            
            if self.tokens >= tokens_requested:
                self.tokens -= tokens_requested
                return True
            
            return False


class AdaptiveRateLimiter:
    """根据系统负载自适应的速率限制器"""
    def __init__(self, base_limit: int, window: int = 60):
        self.base_limit = base_limit
        self.window = window
        self.system_load_factor = 1.0
        self.buckets: Dict[str, TokenBucketRateLimiter] = {}
        self.lock = threading.RLock()
    
    def update_system_load(self, cpu_usage: float, memory_usage: float) -> None:
        """根据系统资源调整速率限制"""
        if cpu_usage > 0.8 or memory_usage > 0.8:
            self.system_load_factor = 0.5  # 降低速率限制
        elif cpu_usage < 0.5 and memory_usage < 0.5:
            self.system_load_factor = 1.2  # 提高速率限制
        else:
            self.system_load_factor = 1.0
    
    def check_rate_limit(self, identifier: str) -> bool:
        """检查速率限制"""
        adjusted_limit = int(self.base_limit * self.system_load_factor)
        
        with self.lock:
            if identifier not in self.buckets:
                self.buckets[identifier] = TokenBucketRateLimiter(
                    capacity=adjusted_limit,
                    refill_rate=adjusted_limit / self.window,
                    window=self.window
                )
            
            return self.buckets[identifier].allow_request()


@dataclass
class RetryStrategy:
    """重试策略配置"""
    max_attempts: int = DefaultConfig.MAX_RETRY_ATTEMPTS
    base_delay: float = 1.0
    max_delay: float = 10.0
    backoff_factor: float = 2.0
    
    def get_delay(self, attempt: int) -> float:
        """计算指数退避延迟"""
        delay = self.base_delay * (self.backoff_factor ** attempt)
        return min(delay, self.max_delay)


# 频率限制记录类
@dataclass
class RateLimitEntry:
    """频率限制记录"""
    count: int = 0
    reset_time: float = 0.0


# MySQL数据库连接配置类
@dataclass
class MySQLConfig:
    """MySQL数据库连接配置"""
    host: str = StringConstants.DEFAULT_HOST
    port: int = DefaultConfig.MYSQL_PORT
    user: str = StringConstants.DEFAULT_USER
    password: str = StringConstants.DEFAULT_PASSWORD
    database: str = StringConstants.DEFAULT_DATABASE
    connection_limit: int = DefaultConfig.CONNECTION_LIMIT
    min_connections: int = DefaultConfig.MIN_CONNECTIONS
    connect_timeout: int = DefaultConfig.CONNECT_TIMEOUT
    idle_timeout: int = DefaultConfig.IDLE_TIMEOUT


# 安全配置类（使用默认值）
@dataclass
class DefaultSecurityConfig:
    """安全配置，用于防止滥用和攻击"""
    max_query_length: int = DefaultConfig.MAX_QUERY_LENGTH
    allowed_query_types: List[str] = field(default_factory=lambda: [
        StringConstants.SQL_SELECT, StringConstants.SQL_SHOW, StringConstants.SQL_DESCRIBE, 
        StringConstants.SQL_INSERT, StringConstants.SQL_UPDATE, StringConstants.SQL_DELETE, 
        StringConstants.SQL_CREATE, StringConstants.SQL_DROP, StringConstants.SQL_ALTER
    ])
    max_result_rows: int = DefaultConfig.MAX_RESULT_ROWS
    query_timeout: int = DefaultConfig.QUERY_TIMEOUT
    rate_limit_window: int = DefaultConfig.RATE_LIMIT_WINDOW
    rate_limit_max: int = DefaultConfig.RATE_LIMIT_MAX


# 连接池管理器
class ConnectionPool:
    """连接池管理器"""
    
    def __init__(self, config: MySQLConfig):
        self.config = config
        self.pool: Optional[mysql.connector.pooling.MySQLConnectionPool] = None
        self.lock = threading.RLock()
        self.health_check_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        self.connection_stats: Dict[str, int] = defaultdict(int)
        
    def initialize(self) -> None:
        """初始化连接池"""
        with self.lock:
            if self.pool is not None:
                return
                
            try:
                pool_config: Dict[str, Any] = {
                    'host': self.config.host,
                    'port': self.config.port,
                    'user': self.config.user,
                    'password': self.config.password,
                    'database': self.config.database,
                    'pool_name': StringConstants.POOL_NAME,
                    'pool_size': self.config.connection_limit,
                    'autocommit': True,
                    'connection_timeout': self.config.connect_timeout,
                    'pool_reset_session': True,
                    'use_unicode': True,
                    'charset': StringConstants.CHARSET,
                    'raise_on_warnings': False,
                    'sql_mode': StringConstants.SQL_MODE
                }
                
                # SSL配置
                if os.getenv(StringConstants.ENV_MYSQL_SSL) == StringConstants.TRUE_STRING:
                    pool_config['ssl_disabled'] = False
                    pool_config['ssl_verify_cert'] = False
                
                self.pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
                self._pre_create_connections()
                self._start_health_check()
                
            except Exception as e:
                raise Exception(f"{StringConstants.MSG_FAILED_TO_INIT_POOL} {e}")
    
    def _pre_create_connections(self) -> None:
        """预创建最小连接数"""
        connections = []
        try:
            if self.pool is not None:
                for _ in range(self.config.min_connections):
                    conn = self.pool.get_connection()
                    connections.append(conn)
                # 释放连接回池
                for conn in connections:
                    conn.close()
        except Exception:
            # 如果预创建失败，不影响整体初始化
            pass
    
    def _start_health_check(self) -> None:
        """启动连接健康检查线程"""
        if self.health_check_thread is None or not self.health_check_thread.is_alive():
            self.health_check_thread = threading.Thread(
                target=self._health_check_loop, daemon=True)
            self.health_check_thread.start()
    
    def _health_check_loop(self) -> None:
        """健康检查循环"""
        while not self.shutdown_event.wait(DefaultConfig.HEALTH_CHECK_INTERVAL):
            try:
                self._perform_health_check()
            except Exception:
                pass
    
    def _perform_health_check(self) -> None:
        """执行健康检查"""
        if not self.pool:
            return
            
        try:
            conn = self.pool.get_connection()
            if conn.is_connected():
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
            conn.close()
        except Exception:
            pass
    
    @contextmanager
    def get_connection(self) -> Any:
        """获取数据库连接的上下文管理器"""
        connection = None
        start_time = time.time()
        
        try:
            if not self.pool:
                self.initialize()
            
            if self.pool is not None:
                connection = self.pool.get_connection()
            
            # 统计连接获取时间
            wait_time = time.time() - start_time
            if wait_time > 0.1:
                self.connection_stats[StringConstants.FIELD_POOL_WAITS] += 1
            else:
                self.connection_stats[StringConstants.FIELD_POOL_HITS] += 1
            
            # 检查连接健康状态
            if connection and not connection.is_connected():
                connection.reconnect(attempts=DefaultConfig.RECONNECT_ATTEMPTS, 
                                   delay=DefaultConfig.RECONNECT_DELAY)
            
            yield connection
            
        finally:
            if connection:
                connection.close()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        if not self.pool:
            return {StringConstants.STATUS_KEY: StringConstants.STATUS_NOT_INITIALIZED}
        
        return {
            StringConstants.FIELD_POOL_NAME: self.pool.pool_name,
            StringConstants.FIELD_POOL_SIZE: self.pool.pool_size,
            StringConstants.FIELD_AVAILABLE_CONNECTIONS: self.pool._cnx_queue.qsize(),
            StringConstants.FIELD_CONNECTION_STATS: dict(self.connection_stats),
            StringConstants.FIELD_HEALTH_CHECK_ACTIVE: self.health_check_thread and self.health_check_thread.is_alive()
        }
    
    def close(self) -> None:
        """关闭连接池"""
        self.shutdown_event.set()
        
        if self.health_check_thread and self.health_check_thread.is_alive():
            self.health_check_thread.join(timeout=5)
        
        with self.lock:
            if self.pool:
                self.pool._remove_connections()
                self.pool = None


class MySQLManager:
    """
    MySQL数据库管理器类
    包含性能监控、智能缓存、重试机制等增强功能
    """
    
    # 类级别预编译正则表达式，避免重复编译
    _DANGEROUS_PATTERNS = [
        re.compile(r'\b(LOAD_FILE|INTO OUTFILE|INTO DUMPFILE)\b', re.IGNORECASE),
        re.compile(r'\b(SYSTEM|EXEC|SHELL)\b', re.IGNORECASE),
        re.compile(r'\bINTO\s+OUTFILE\b', re.IGNORECASE),
        re.compile(r'\bLOAD\s+DATA\b', re.IGNORECASE),
    ]
    _TABLE_NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    _SANITIZE_PATTERNS = [
        (re.compile(r'password[^\s]*', re.IGNORECASE), 'password=***'),
        (re.compile(r'host[^\s]*', re.IGNORECASE), 'host=***'),
        (re.compile(r'user[^\s]*', re.IGNORECASE), 'user=***'),
    ]
    
    def __init__(self) -> None:
        """初始化MySQL管理器"""
        self.session_id = str(uuid.uuid4())
        
        # 集中配置管理
        self.config_manager = ConfigurationManager()
        
        # 从配置管理器加载配置
        self.config = MySQLConfig(
            host=self.config_manager.database.host,
            port=self.config_manager.database.port,
            user=self.config_manager.database.user,
            password=self.config_manager.database.password,
            database=self.config_manager.database.database,
            connection_limit=self.config_manager.database.connection_limit,
            connect_timeout=self.config_manager.database.connect_timeout,
            idle_timeout=self.config_manager.database.idle_timeout
        )
        
        # 安全配置（使用配置管理器）
        self.security_config = DefaultSecurityConfig(
            max_query_length=self.config_manager.security.max_query_length,
            allowed_query_types=self.config_manager.security.allowed_query_types,
            max_result_rows=self.config_manager.security.max_result_rows,
            query_timeout=self.config_manager.security.query_timeout,
            rate_limit_window=self.config_manager.security.rate_limit_window,
            rate_limit_max=self.config_manager.security.rate_limit_max
        )
        
        # 连接池
        self.connection_pool = ConnectionPool(self.config)
        
        # 智能缓存（使用配置管理器）
        self.schema_cache = SmartCache(
            max_size=self.config_manager.cache.schema_cache_size,
            ttl=self.config_manager.cache.cache_ttl
        )
        self.table_exists_cache = SmartCache(
            max_size=self.config_manager.cache.table_exists_cache_size,
            ttl=self.config_manager.cache.cache_ttl
        )
        self.index_cache = SmartCache(
            max_size=self.config_manager.cache.index_cache_size,
            ttl=self.config_manager.cache.cache_ttl
        )
        
        # 性能指标（保持向后兼容）
        self.metrics = PerformanceMetrics()
        self.metrics_lock = threading.RLock()
        
        # 增强指标管理器
        self.enhanced_metrics = EnhancedMetricsManager()
        self.enhanced_metrics.start_monitoring()
        
        # 增强安全验证器
        self.security_validator = EnhancedSecurityValidator()
        
        # 自适应频率限制器
        self.adaptive_rate_limiter = AdaptiveRateLimiter(
            base_limit=self.security_config.rate_limit_max,
            window=self.security_config.rate_limit_window
        )
        
        # 重试策略
        self.retry_strategy = RetryStrategy()
    
    def _update_metrics(self, query_time: float, is_error: bool = False, is_slow: bool = False) -> None:
        """更新性能指标（同时更新传统指标和增强指标）"""
        with self.metrics_lock:
            self.metrics.query_count += 1
            self.metrics.total_query_time += query_time
            if is_error:
                self.metrics.error_count += 1
                # 记录到增强指标
                self.enhanced_metrics.record_error("query_error", "medium")
            if is_slow:
                self.metrics.slow_query_count += 1
        
        # 记录到增强指标管理器
        self.enhanced_metrics.record_query_time(query_time)
        
        # 更新缓存命中率指标
        cache_hit_rate = self.metrics.get_cache_hit_rate()
        self.enhanced_metrics.record_cache_hit_rate(cache_hit_rate)
    
    def _execute_with_retry(self, operation: Callable, *args: Any, **kwargs: Any) -> Any:
        """带重试机制的操作执行"""
        last_exception = None
        
        for attempt in range(self.retry_strategy.max_attempts):
            try:
                return operation(*args, **kwargs)
            except (MySQLError, Exception) as e:
                last_exception = e
                
                # 某些错误不应该重试
                if isinstance(e, MySQLError):
                    if e.errno in [MySQLErrorCodes.ACCESS_DENIED, MySQLErrorCodes.PARSE_ERROR]:
                        break
                
                if attempt < self.retry_strategy.max_attempts - 1:
                    delay = self.retry_strategy.get_delay(attempt)
                    time.sleep(delay)
        
        if last_exception:
            raise last_exception
        else:
            raise Exception("Operation failed after all retries")
    
    def _validate_input(self, input_value: Any, field_name: str, validation_level: str = "strict") -> None:
        """使用增强验证器进行输入验证"""
        self.security_validator.validate_input_comprehensive(input_value, field_name, validation_level)
    
    def _validate_query(self, query: str) -> None:
        """验证SQL查询的安全性"""
        if len(query) > self.security_config.max_query_length:
            raise ValueError(StringConstants.MSG_QUERY_TOO_LONG)
        
        # 使用类级别预编译的正则表达式
        for pattern in self._DANGEROUS_PATTERNS:
            if pattern.search(query):
                raise ValueError(StringConstants.MSG_PROHIBITED_OPERATIONS)
        
        # 优化：只获取第一个单词，避免完整分割
        first_word_end = query.find(' ')
        query_type = query[:first_word_end].strip().upper() if first_word_end != -1 else query.strip().upper()
        
        if query_type not in self.security_config.allowed_query_types:
            raise ValueError(StringConstants.MSG_QUERY_TYPE_NOT_ALLOWED.format(query_type=query_type))
    
    def _validate_table_name(self, table_name: str) -> None:
        """验证表名的合法性"""
        if not self._TABLE_NAME_PATTERN.match(table_name):
            raise ValueError(StringConstants.MSG_INVALID_TABLE_NAME)
        
        if len(table_name) > DefaultConfig.MAX_TABLE_NAME_LENGTH:
            raise ValueError(StringConstants.MSG_TABLE_NAME_TOO_LONG)
    
    def _check_rate_limit(self, identifier: str = "default") -> None:
        """使用自适应速率限制器检查请求频率"""
        if not self.adaptive_rate_limiter.check_rate_limit(identifier):
            raise ValueError(StringConstants.MSG_RATE_LIMIT_EXCEEDED)
    
    def _get_table_schema_cached(self, table_name: str) -> Any:
        """获取表结构的缓存版本"""
        cache_key = f"schema_{table_name}"
        result = self.schema_cache.get(cache_key)
        
        if result is None:
            # 表结构查询SQL
            schema_query = """
                SELECT 
                    COLUMN_NAME,
                    DATA_TYPE,
                    IS_NULLABLE,
                    COLUMN_DEFAULT,
                    COLUMN_KEY,
                    EXTRA,
                    COLUMN_COMMENT
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
                ORDER BY ORDINAL_POSITION
            """
            result = self.execute_query(schema_query, [table_name])
            self.schema_cache.put(cache_key, result)
            with self.metrics_lock:
                self.metrics.cache_misses += 1
        else:
            with self.metrics_lock:
                self.metrics.cache_hits += 1
        
        return result
    
    def _table_exists_cached(self, table_name: str) -> bool:
        """检查表是否存在的缓存版本"""
        cache_key = f"exists_{table_name}"
        result = self.table_exists_cache.get(cache_key)
        
        if result is None:
            # 表存在性检查查询SQL
            exists_query = """
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
            """
            query_result = self.execute_query(exists_query, [table_name])
            result = query_result[0]['count'] > 0 if query_result else False
            self.table_exists_cache.put(cache_key, result)
            with self.metrics_lock:
                self.metrics.cache_misses += 1
        else:
            with self.metrics_lock:
                self.metrics.cache_hits += 1
        
        return result
    
    def execute_query(self, query: str, params: Optional[List[Any]] = None) -> Any:
        """执行SQL查询的通用方法"""
        start_time = time.time()
        
        try:
            # 检查请求频率限制
            self._check_rate_limit()
            
            # 验证查询安全性
            self._validate_query(query)
            
            # 使用重试机制执行查询
            result = self._execute_with_retry(self._execute_query_internal, query, params)
            
            # 更新性能指标
            query_time = time.time() - start_time
            is_slow = query_time > DefaultConfig.SLOW_QUERY_THRESHOLD
            self._update_metrics(query_time, is_slow=is_slow)
            
            return result
            
        except Exception as e:
            query_time = time.time() - start_time
            self._update_metrics(query_time, is_error=True)
            raise
    
    def _execute_query_internal(self, query: str, params: Optional[List[Any]] = None) -> Any:
        """内部查询执行方法，使用流式处理优化内存使用"""
        with self.connection_pool.get_connection() as connection:
            cursor = connection.cursor(dictionary=True, buffered=False)  # 使用非缓冲模式
            
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if cursor.with_rows:
                    return self._stream_results(cursor)
                else:
                    return {"affected_rows": cursor.rowcount, "last_insert_id": getattr(cursor, 'lastrowid', None)}
            
            finally:
                cursor.close()
    
    def _stream_results(self, cursor: Any) -> List[Dict[str, Any]]:
        """内存友好的结果流式处理"""
        results = []
        row_count = 0
        
        # 使用迭代器而非批量加载，更节省内存
        for row in cursor:
            if row_count >= self.security_config.max_result_rows:
                break
            results.append(row)
            row_count += 1
        
        return results
    
    def invalidate_caches(self, operation_type: str = "DDL", table_name: Optional[str] = None) -> None:
        """统一缓存失效管理"""
        if operation_type in ["DDL", "CREATE", "DROP", "ALTER"]:
            # DDL操作清理所有缓存
            self._clear_all_caches()
        elif operation_type == "DML" and table_name:
            # DML操作只清理特定表的缓存
            self._invalidate_table_specific_cache(table_name)
    
    def _clear_all_caches(self) -> None:
        """清理所有缓存"""
        for cache in [self.schema_cache, self.table_exists_cache, self.index_cache]:
            cache.clear()
    
    def _invalidate_table_specific_cache(self, table_name: str) -> None:
        """失效特定表的缓存（优化版本，减少重复代码）"""
        cache_keys_to_remove = [
            f"schema_{table_name}",
            f"exists_{table_name}",
            f"indexes_{table_name}"
        ]
        
        # 使用统一的方法清理所有缓存中的相关键
        caches = [self.schema_cache, self.table_exists_cache, self.index_cache]
        for cache in caches:
            with cache.lock:
                for key in cache_keys_to_remove:
                    cache.cache.pop(key, None)
    
    def invalidate_specific_table_cache(self, table_name: str) -> None:
        """保持向后兼容性的接口"""
        self._invalidate_table_specific_cache(table_name)
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        with self.metrics_lock:
            return {
                StringConstants.SECTION_PERFORMANCE: self.metrics.to_dict(),
                StringConstants.SECTION_CACHE_STATS: {
                    StringConstants.SECTION_SCHEMA_CACHE: self.schema_cache.get_stats(),
                    StringConstants.SECTION_TABLE_EXISTS_CACHE: self.table_exists_cache.get_stats(),
                    StringConstants.SECTION_INDEX_CACHE: self.index_cache.get_stats()
                },
                StringConstants.SECTION_CONNECTION_POOL: self.connection_pool.get_stats()
            }
    
    def close(self) -> None:
        """关闭数据库管理器"""
        try:
            # 停止增强指标监控
            self.enhanced_metrics.stop_monitoring()
            
            self.connection_pool.close()
            
            # 清理缓存
            self.schema_cache.clear()
            self.table_exists_cache.clear()
            self.index_cache.clear()
            
            # 清理自适应速率限制器
            with self.adaptive_rate_limiter.lock:
                self.adaptive_rate_limiter.buckets.clear()
                
        except Exception as e:
            print(f"{StringConstants.MSG_ERROR_DURING_CLEANUP} {e}", file=sys.stderr)


# 创建全局MySQL管理器实例
mysql_manager = MySQLManager()

# 创建FastMCP服务器
mcp = FastMCP(StringConstants.SERVER_NAME)


@mcp.tool
def mysql_query(query: str, params: Optional[List[Any]] = None) -> str:
    """
    执行MySQL查询（SELECT, SHOW, DESCRIBE等）
    
    参数:
        query: 要执行的SQL查询语句
        params: 预处理语句的可选参数
    
    返回:
        包含查询结果的JSON字符串
    """
    try:
        if params is None:
            params = []
        
        # 验证参数
        mysql_manager._validate_input(query, "query")
        for i, param in enumerate(params):
            mysql_manager._validate_input(param, f"param_{i}")
        
        result = mysql_manager.execute_query(query, params)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_QUERY_FAILED} {str(e)}")


@mcp.tool
def mysql_show_tables() -> str:
    """
    显示当前数据库中的所有表
    
    返回:
        包含表列表的JSON字符串
    """
    try:
        show_tables_query = "SHOW TABLES"
        result = mysql_manager.execute_query(show_tables_query)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_SHOW_TABLES_FAILED} {str(e)}")


@mcp.tool
def mysql_describe_table(table_name: str) -> str:
    """
    描述指定表的结构
    
    参数:
        table_name: 要描述的表名
    
    返回:
        包含表结构的JSON字符串
    """
    try:
        mysql_manager._validate_table_name(table_name)
        result = mysql_manager._get_table_schema_cached(table_name)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_DESCRIBE_TABLE_FAILED} {str(e)}")

@mcp.tool
def mysql_select_data(table_name: str, columns: Optional[List[str]] = None, where_clause: Optional[str] = None, limit: Optional[int] = None) -> str:
    """从表中选择数据，支持可选条件"""
    try:
        mysql_manager._validate_table_name(table_name)
        
        if columns is None:
            columns = ["*"]
        
        for col in columns:
            if col != "*":
                mysql_manager._validate_input(col, "column")
        
        query = f"SELECT {', '.join(columns)} FROM `{table_name}`"
        
        if where_clause:
            mysql_manager._validate_input(where_clause, "where_clause")
            query += f" WHERE {where_clause}"
        
        if limit:
            query += f" LIMIT {int(limit)}"
        
        result = mysql_manager.execute_query(query)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_SELECT_DATA_FAILED} {str(e)}")


@mcp.tool
def mysql_insert_data(table_name: str, data: Dict[str, Any]) -> str:
    """向表中插入新数据"""
    try:
        mysql_manager._validate_table_name(table_name)
        
        for key, value in data.items():
            mysql_manager._validate_input(key, "column_name")
            mysql_manager._validate_input(value, "column_value")
        
        columns = list(data.keys())
        values = list(data.values())
        placeholders = ", ".join(["%s"] * len(values))
        
        query = f"INSERT INTO `{table_name}` (`{'`, `'.join(columns)}`) VALUES ({placeholders})"
        result = mysql_manager.execute_query(query, values)
        
        return json.dumps({StringConstants.SUCCESS_KEY: True, **result}, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_INSERT_DATA_FAILED} {str(e)}")


@mcp.tool
def mysql_update_data(table_name: str, data: Dict[str, Any], where_clause: str) -> str:
    """
    更新表中的现有数据
    
    参数:
        table_name: 要更新的表名
        data: 列名和新值的键值对字典
        where_clause: WHERE子句，指定要更新的记录（不包含WHERE关键字）
    
    返回:
        包含更新结果的JSON字符串
    """
    try:
        mysql_manager._validate_table_name(table_name)
        mysql_manager._validate_input(where_clause, "where_clause")
        
        for key, value in data.items():
            mysql_manager._validate_input(key, "column_name")
            mysql_manager._validate_input(value, "column_value")
        
        columns = list(data.keys())
        values = list(data.values())
        set_clause = ", ".join([f"`{col}` = %s" for col in columns])
        
        query = f"UPDATE `{table_name}` SET {set_clause} WHERE {where_clause}"
        result = mysql_manager.execute_query(query, values)
        
        return json.dumps({StringConstants.SUCCESS_KEY: True, **result}, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_UPDATE_DATA_FAILED} {str(e)}")


@mcp.tool
def mysql_delete_data(table_name: str, where_clause: str) -> str:
    """
    从表中删除数据
    
    参数:
        table_name: 要删除数据的表名
        where_clause: WHERE子句，指定要删除的记录（不包含WHERE关键字）
    
    返回:
        包含删除结果的JSON字符串
    """
    try:
        mysql_manager._validate_table_name(table_name)
        mysql_manager._validate_input(where_clause, "where_clause")
        
        query = f"DELETE FROM `{table_name}` WHERE {where_clause}"
        result = mysql_manager.execute_query(query)
        
        return json.dumps({StringConstants.SUCCESS_KEY: True, **result}, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_DELETE_DATA_FAILED} {str(e)}")


@mcp.tool
def mysql_get_schema(table_name: Optional[str] = None) -> str:
    """
    获取数据库模式信息，包括表、列和约束
    
    参数:
        table_name: 可选的特定表名，用于获取该表的模式信息
    
    返回:
        包含模式信息的JSON字符串
    """
    try:
        query = """
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_KEY,
                EXTRA,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE()
        """
        
        params: List[str] = []
        if table_name:
            mysql_manager._validate_table_name(table_name)
            query += " AND TABLE_NAME = %s"
            params.append(table_name)
        
        query += " ORDER BY TABLE_NAME, ORDINAL_POSITION"
        
        result = mysql_manager.execute_query(query, params if params else None)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_GET_SCHEMA_FAILED} {str(e)}")


@mcp.tool
def mysql_get_indexes(table_name: Optional[str] = None) -> str:
    """
    获取表或所有表的索引信息
    
    参数:
        table_name: 可选的特定表名，用于获取该表的索引信息
    
    返回:
        包含索引信息的JSON字符串
    """
    try:
        if table_name:
            mysql_manager._validate_table_name(table_name)
            # 使用缓存版本提高性能
            cache_key = f"indexes_{table_name}"
            cached_result = mysql_manager.index_cache.get(cache_key)
            
            if cached_result is None:
                # 索引信息查询SQL
                indexes_query = """
                    SELECT 
                        INDEX_NAME,
                        COLUMN_NAME,
                        NON_UNIQUE,
                        SEQ_IN_INDEX,
                        INDEX_TYPE
                    FROM INFORMATION_SCHEMA.STATISTICS 
                    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = %s
                    ORDER BY INDEX_NAME, SEQ_IN_INDEX
                """
                query_result = mysql_manager.execute_query(indexes_query, [table_name])
                result = json.dumps(query_result, indent=2, default=str)
                mysql_manager.index_cache.put(cache_key, result)
                with mysql_manager.metrics_lock:
                    mysql_manager.metrics.cache_misses += 1
            else:
                result = cached_result  # cached_result is a string
                with mysql_manager.metrics_lock:
                    mysql_manager.metrics.cache_hits += 1
            
            return result
        else:
            # 获取所有表的索引信息，不使用缓存
            query = """
                SELECT 
                    TABLE_NAME,
                    INDEX_NAME,
                    COLUMN_NAME,
                    NON_UNIQUE,
                    SEQ_IN_INDEX,
                    INDEX_TYPE
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = DATABASE()
                ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
            """
            result = mysql_manager.execute_query(query)
            return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_GET_INDEXES_FAILED} {str(e)}")


@mcp.tool
def mysql_get_foreign_keys(table_name: Optional[str] = None) -> str:
    """
    获取表或所有表的外键约束信息
    
    参数:
        table_name: 可选的特定表名，用于获取该表的外键信息
    
    返回:
        包含外键信息的JSON字符串
    """
    try:
        query = """
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME,
                UPDATE_RULE,
                DELETE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
        """
        
        params: List[str] = []
        if table_name:
            mysql_manager._validate_table_name(table_name)
            query += " AND TABLE_NAME = %s"
            params.append(table_name)
        
        query += " ORDER BY TABLE_NAME, CONSTRAINT_NAME"
        
        result = mysql_manager.execute_query(query, params if params else None)
        return json.dumps(result, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_GET_FOREIGN_KEYS_FAILED} {str(e)}")


@mcp.tool
def mysql_create_table(table_name: str, columns: List[Dict[str, Any]]) -> str:
    """
    创建带有指定列和约束的新表
    
    参数:
        table_name: 要创建的表名
        columns: 列定义数组，包含键：name, type, nullable, default, primary_key, auto_increment
    
    返回:
        包含创建结果的JSON字符串
    """
    try:
        mysql_manager._validate_table_name(table_name)
        
        column_defs = []
        for col in columns:
            mysql_manager._validate_input(col.get('name', ''), 'column_name')
            mysql_manager._validate_input(col.get('type', ''), 'column_type')
            
            definition = f"`{col['name']}` {col['type']}"
            
            if not col.get('nullable', True):
                definition += " NOT NULL"
            if col.get('auto_increment', False):
                definition += " AUTO_INCREMENT"
            if col.get('default'):
                definition += f" DEFAULT {col['default']}"
            
            column_defs.append(definition)
        
        primary_keys = [col['name'] for col in columns if col.get('primary_key', False)]
        if primary_keys:
            column_defs.append(f"PRIMARY KEY (`{'`, `'.join(primary_keys)}`)")
        
        query = f"CREATE TABLE `{table_name}` ({', '.join(column_defs)})"
        result = mysql_manager.execute_query(query)
        
        # 使用统一缓存清理
        mysql_manager.invalidate_caches("CREATE")
        
        return json.dumps({StringConstants.SUCCESS_KEY: True, **result}, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_CREATE_TABLE_FAILED} {str(e)}")


@mcp.tool
def mysql_drop_table(table_name: str, if_exists: bool = True) -> str:
    """
    从数据库删除（丢弃）表
    
    参数:
        table_name: 要删除的表名
        if_exists: 使用IF EXISTS子句以避免表不存在时的错误
    
    返回:
        包含删除结果的JSON字符串
    """
    try:
        mysql_manager._validate_table_name(table_name)
        
        query = f"DROP TABLE {StringConstants.SQL_IF_EXISTS if if_exists else ''} `{table_name}`"
        result = mysql_manager.execute_query(query)
        
        # 使用统一缓存清理
        mysql_manager.invalidate_caches("DROP")
        
        return json.dumps({StringConstants.SUCCESS_KEY: True, **result}, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_DROP_TABLE_FAILED} {str(e)}")


@mcp.tool
def mysql_diagnose_connection() -> str:
    """
    诊断MySQL连接状态和配置（使用集中配置管理）
    
    返回:
        包含诊断信息的JSON字符串
    """
    try:
        diagnosis = {
            StringConstants.FIELD_CONNECTION_POOL_STATUS: mysql_manager.connection_pool.get_stats(),
            StringConstants.FIELD_CONFIG: mysql_manager.config_manager.to_dict(),
            StringConstants.FIELD_PERFORMANCE_METRICS: mysql_manager.get_performance_metrics(),
            "enhanced_metrics": mysql_manager.enhanced_metrics.get_comprehensive_metrics()
        }
        
        # 尝试执行简单的连接测试
        try:
            connection_test_query = "SELECT 1 as test_connection"
            test_result = mysql_manager.execute_query(connection_test_query)
            diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
                StringConstants.STATUS_KEY: StringConstants.STATUS_SUCCESS,
                StringConstants.FIELD_RESULT: test_result
            }
        except Exception as e:
            diagnosis[StringConstants.FIELD_CONNECTION_TEST] = {
                StringConstants.STATUS_KEY: StringConstants.STATUS_FAILED,
                StringConstants.ERROR_KEY: str(e)
            }
        
        return json.dumps(diagnosis, indent=2, default=str)
    
    except Exception as e:
        raise Exception(f"{StringConstants.MSG_DIAGNOSE_FAILED} {str(e)}")


def signal_handler(signum: Any, frame: Any) -> None:
    """优雅关闭处理器"""
    print(f"\n{StringConstants.MSG_SIGNAL_RECEIVED} {signum}, {StringConstants.MSG_GRACEFUL_SHUTDOWN}", file=sys.stderr)
    mysql_manager.close()
    exit(0)


def main() -> None:
    """主函数"""
    signal.signal(signal.SIGINT, signal_handler)
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        print(StringConstants.MSG_SERVER_RUNNING, file=sys.stderr)
        mcp.run()
    except Exception as e:
        print(f"{StringConstants.MSG_SERVER_ERROR} {e}", file=sys.stderr)
        mysql_manager.close()
        exit(1)


if __name__ == "__main__":
    main()