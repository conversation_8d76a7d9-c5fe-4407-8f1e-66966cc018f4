# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Server
```bash
python server.py
```

### Environment Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Or install in development mode with optional dev dependencies
pip install -e ".[dev]"

# Copy environment template and configure
cp .env.example .env
```

### Code Quality (if dev dependencies installed)
```bash
# Code formatting
black server.py constants.py

# Type checking  
mypy server.py

# Testing
pytest tests/
```

### Project Structure
Key files and their purposes:
- `server.py`: Main FastMCP server with MySQLManager and all MCP tools
- `constants.py`: Centralized constants (MySQLErrorCodes, DefaultConfig, StringConstants)
- `pyproject.toml`: Project configuration with dependencies and tool settings
- `.env.example`: Environment variable template with performance tuning examples
- `requirements.txt`: Direct dependency specification

## Core Architecture

### MySQLManager - Central Engine
The `MySQLManager` class in `server.py` is the core orchestrator containing:
- **ConnectionPool**: Pre-created connections with health monitoring thread
- **SmartCache system**: Three specialized LRU caches (SchemaCache, TableExistsCache, IndexCache) 
- **PerformanceMetrics**: Real-time query performance and cache efficiency tracking
- **EnhancedMetricsManager**: Advanced time-series metrics with alerting system
- **SecurityConfig**: Multi-layer input validation and rate limiting
- **RetryStrategy**: Exponential backoff for transient failures
- **ConfigurationManager**: Centralized configuration management with validation

Key singleton pattern - access via global `mysql_manager` instance.

### Caching System Architecture
Three-tier intelligent caching with automatic invalidation:
1. **SchemaCache** (128 entries): Table structure metadata, invalidated on DDL operations
2. **TableExistsCache** (64 entries): Fast boolean existence checks
3. **IndexCache** (64 entries): Index metadata with smart refresh

All caches use TTL (300s default) and provide hit/miss statistics for performance monitoring.

### Configuration Management
All constants centralized in `constants.py`:
- **DefaultConfig**: Database connection, security, performance parameters
- **StringConstants**: Environment variables, error messages, SQL keywords
- Zero magic numbers philosophy - all values explicitly defined

### MCP Tools Structure
12 @mcp.tool functions organized in groups:
- **Core Data**: mysql_query, mysql_show_tables, mysql_describe_table, mysql_select_data, CRUD operations
- **Schema Management**: mysql_get_schema, mysql_get_indexes, mysql_get_foreign_keys, DDL operations
- **Diagnostics**: mysql_diagnose_connection (comprehensive system health)

### Security Architecture
Multi-layer protection system:
- **Input Validation**: Null byte filtering, length limits, dangerous pattern detection
- **MySQL Error Classification**: Intelligent error categorization in MySQLErrorCodes class
- **Rate Limiting**: Sliding window algorithm (60s windows, 100 requests/min default)
- **Parameterized Queries**: All SQL uses prepared statements

### Performance Monitoring
Enhanced dual-layer performance tracking:
- **PerformanceMetrics class**: Legacy metrics for backward compatibility
- **EnhancedMetricsManager**: Time-series metrics with 1000-point retention
- **TimeSeriesMetrics**: Individual metric collectors with percentile statistics
- **System Resource Monitoring**: CPU and memory tracking (requires psutil)
- **Alert System**: Configurable thresholds for slow queries, high error rates, low cache hits

Tracks:
- Query execution times with slow query detection (1s threshold default)
- Cache efficiency metrics (target >80% hit rate) 
- Connection pool utilization statistics
- Error rates and classification trending
- P95/P99 percentiles for query performance analysis

## Development Patterns

### Error Handling
- All exceptions wrapped in descriptive messages using StringConstants
- MySQL errors classified by error codes for intelligent retry logic
- Security events logged with sanitized output (credentials masked)

### Resource Management
- Connection pool automatically manages MySQL connections
- Context managers used throughout for cleanup
- Graceful shutdown handling via signal handlers
- Cache cleanup and memory management on exit

### Thread Safety
- RLock protection for all shared resources (caches, metrics, rate limiters)
- Background health check thread runs independently
- Session-based tracking with UUID for concurrent operations

## Configuration

### Environment Variables
Key configuration via `.env` file (see `.env.example`):
- **Database**: MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE
- **Performance**: SCHEMA_CACHE_SIZE, CACHE_TTL, MYSQL_CONNECTION_LIMIT, BATCH_SIZE
- **Security**: MAX_QUERY_LENGTH, RATE_LIMIT_MAX, ALLOWED_QUERY_TYPES
- **Monitoring**: SLOW_QUERY_THRESHOLD

### Performance Tuning Templates
Three pre-defined configuration templates in documentation:
- **Enterprise**: High concurrency (50 connections, 512 cache size)
- **Medium-scale**: Balanced (20 connections, 128 cache size)  
- **Resource-constrained**: Low footprint (5 connections, 32 cache size)

## Code Modification Guidelines

### Adding New Tools
1. Add @mcp.tool decorator to function
2. Use mysql_manager for all database operations
3. Validate inputs using mysql_manager._validate_input()
4. Handle exceptions and wrap in descriptive StringConstants messages
5. For DDL operations, invalidate relevant caches using mysql_manager.invalidate_caches()
6. Update both legacy and enhanced metrics as needed

### Extending Caching
- Inherit from SmartCache base class for LRU + TTL behavior
- Add cache invalidation logic for operations that modify cached data
- Register new cache types in MySQLManager.__init__()
- Use OrderedDict for O(1) LRU operations

### Performance Monitoring
- Add new metrics to both PerformanceMetrics and EnhancedMetricsManager
- Update mysql_diagnose_connection() to include new metrics
- Consider cache vs real-time tradeoffs for expensive operations
- Use TimeSeriesMetrics for historical trend analysis

### Security Considerations
- All user inputs must go through EnhancedSecurityValidator validation pipeline
- Add new dangerous patterns to DANGEROUS_PATTERNS and INJECTION_PATTERNS
- Never log sensitive information (use sanitization methods in _SANITIZE_PATTERNS)
- Test rate limiting behavior for new high-frequency operations
- Use validation_level parameter for different security strictness levels

## Common Development Tasks

### Adding a New MCP Tool
1. Create a new function with @mcp.tool decorator in server.py
2. Validate all inputs using mysql_manager._validate_input()
3. Use mysql_manager.execute_query() for database operations
4. Handle exceptions appropriately with descriptive error messages
5. Return results as JSON strings
6. For DDL operations, call mysql_manager.invalidate_caches() after execution

### Modifying Cache Behavior
1. Update cache size and TTL in DefaultConfig constants
2. Modify cache invalidation logic in invalidate_caches() method
3. Add new cache types by inheriting from SmartCache
4. Update cache statistics in PerformanceMetrics

### Extending Security Validation
1. Add new patterns to EnhancedSecurityValidator.DANGEROUS_PATTERNS
2. Update _validate_input() method with new validation rules
3. Add new error messages to StringConstants for validation failures
4. Test with various input scenarios to ensure proper protection

### Performance Optimization
1. Profile slow operations using EnhancedMetricsManager
2. Optimize database queries with proper indexing
3. Adjust cache sizes and TTL values based on usage patterns
4. Tune connection pool settings for optimal throughput
5. Monitor system resources with psutil integration