# MySQL MCP服务器 - 环境变量配置示例
# 复制此文件为 .env 并根据你的环境更新配置值

# =============================================================================
# 数据库连接配置
# =============================================================================
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password_here
MYSQL_DATABASE=your_database_name

# 连接池设置
MYSQL_CONNECTION_LIMIT=10          # 连接池最大连接数
MYSQL_CONNECT_TIMEOUT=60           # 连接超时时间（秒）
MYSQL_IDLE_TIMEOUT=60              # 空闲连接超时时间（秒）

# SSL配置（取消注释以启用）
# MYSQL_SSL=true

# =============================================================================
# 安全配置
# =============================================================================

# 查询安全限制
MAX_QUERY_LENGTH=10000             # 单个查询的最大字符长度
MAX_RESULT_ROWS=1000               # 查询结果的最大行数
QUERY_TIMEOUT=30                   # 查询执行超时时间（秒）

# 允许的SQL操作类型（逗号分隔）
ALLOWED_QUERY_TYPES=SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER

# 频率限制
RATE_LIMIT_WINDOW=60               # 频率限制时间窗口（秒）
RATE_LIMIT_MAX=100                 # 时间窗口内最大请求数

# =============================================================================
# 性能与缓存配置
# =============================================================================

# 缓存大小设置
SCHEMA_CACHE_SIZE=128              # 表结构缓存最大条目数
TABLE_EXISTS_CACHE_SIZE=64         # 表存在性检查缓存大小
INDEX_CACHE_SIZE=64                # 索引信息缓存大小
CACHE_TTL=300                      # 缓存过期时间（秒）

# 性能监控设置
SLOW_QUERY_THRESHOLD=1.0           # 慢查询识别阈值（秒）
METRICS_WINDOW_SIZE=1000           # 性能指标窗口大小
BATCH_SIZE=1000                    # 批处理大小

# 连接池健康监控
HEALTH_CHECK_INTERVAL=30           # 健康检查间隔（秒）
CONNECTION_MAX_AGE=3600            # 连接最大存活时间（秒）

# 重试配置
MAX_RETRY_ATTEMPTS=3               # 最大重试次数
RECONNECT_ATTEMPTS=3               # 重连尝试次数
RECONNECT_DELAY=1                  # 重连延迟（秒）

# =============================================================================
# 日志与调试配置
# =============================================================================

# 日志级别（DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO
MAX_LOG_DETAIL_LENGTH=100          # 日志详情最大长度

# =============================================================================
# 不同环境的配置示例
# =============================================================================

# --- 开发环境配置 ---
# 适合本地开发，启用调试日志和较小的限制
# MYSQL_HOST=localhost
# MYSQL_CONNECTION_LIMIT=5
# MAX_RESULT_ROWS=500
# RATE_LIMIT_MAX=50
# CACHE_TTL=180
# LOG_LEVEL=DEBUG

# --- 生产环境配置 ---
# 适合生产部署，启用SSL和增强安全性
# MYSQL_HOST=prod-mysql-server
# MYSQL_CONNECTION_LIMIT=20
# MAX_RESULT_ROWS=2000
# RATE_LIMIT_MAX=200
# SCHEMA_CACHE_SIZE=256
# MYSQL_SSL=true
# LOG_LEVEL=WARNING

# --- 高性能环境配置 ---
# 适合高负载工作场景，大缓存和高并发
# MYSQL_CONNECTION_LIMIT=50
# MAX_RESULT_ROWS=5000
# RATE_LIMIT_MAX=500
# SCHEMA_CACHE_SIZE=512
# TABLE_EXISTS_CACHE_SIZE=256
# INDEX_CACHE_SIZE=256
# BATCH_SIZE=2000
# SLOW_QUERY_THRESHOLD=0.3

# --- 内存受限环境配置 ---
# 适合内存有限的环境，减少缓存使用
# MYSQL_CONNECTION_LIMIT=3
# MAX_RESULT_ROWS=200
# RATE_LIMIT_MAX=20
# SCHEMA_CACHE_SIZE=32
# TABLE_EXISTS_CACHE_SIZE=16
# INDEX_CACHE_SIZE=16
# BATCH_SIZE=100
# CACHE_TTL=120