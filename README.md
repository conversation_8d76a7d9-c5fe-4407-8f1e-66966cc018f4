# MySQL MCP Server - FastMCP Edition

A high-performance, enterprise-grade MySQL database operations server designed specifically for the Model Context Protocol (MCP). Built with the FastMCP framework, it integrates intelligent caching, performance monitoring, enhanced connection pool management, and comprehensive security protection mechanisms.

## 🚀 Core Features

### ⚡ High-Performance Architecture
- **FastMCP Framework**: Built with modern FastMCP for superior performance and reliability
- **Intelligent Caching System**: Multi-level LRU caching with TTL and access statistics
  - Table schema caching (128 entries)
  - Table existence checks (64 entries)
  - Index information caching (64 entries)
- **Enhanced Connection Pool**: Pre-created connections, health checks, and automatic reconnection
- **Optimized Batch Processing**: Smart data fetching with configurable batch sizes

### 🧠 Advanced Features
- **Enhanced Retry Mechanism**: Exponential backoff retry strategy with configurable attempts (max 3)
- **Adaptive Rate Limiting**: Token bucket algorithm with system load adjustment
- **Query Optimization**: Slow query detection (configurable threshold, default 1.0s) and performance analysis
- **Concurrency Control**: Thread-safe resource management with RLock
- **Real-time Performance Monitoring**: Time-series metrics collection with 1000-point history
- **Enhanced Security Validation**: Multi-level input validation with SQL injection pattern detection
- **System Resource Monitoring**: CPU and memory usage tracking (requires psutil)

### 🛡️ Enterprise Security
- **Multi-Layer Protection**: Input validation, SQL injection protection, dangerous pattern detection
- **MySQL Error Classification**: Intelligent error code analysis and categorization
- **Security Auditing**: Comprehensive operation logging and security event tracking
- **Sensitive Information Protection**: Automatic credential masking and log sanitization

### 🔧 Zero-Configuration Architecture
- **Constant-Based Design**: All magic numbers and strings standardized
- **Configuration Separation**: Independent management of MySQL, security, and performance configurations
- **Environment Variable Driven**: Complete environment variable configuration support
- **Zero Magic Numbers**: All constants standardized for improved maintainability

## Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Claude Desktop Integration](#claude-desktop-integration)
- [API Tools Reference](#api-tools-reference)
- [Usage Examples](#usage-examples)
- [Architecture Design](#architecture-design)
- [Performance Monitoring](#performance-monitoring)
- [Caching Strategy](#caching-strategy)
- [Security Features](#security-features)
- [Troubleshooting](#troubleshooting)
- [Performance Tuning](#performance-tuning)
- [Development Guide](#development-guide)
- [License](#license)

## Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd MySQL_MCP_FastMCP
```

### 2. Install Dependencies

Using pip:
```bash
pip install -r requirements.txt
```

Or install with project configuration:
```bash
pip install -e .
```

### 3. Configure Environment Variables
Copy the provided template and create a `.env` file:
```bash
cp .env.example .env
```

Then edit the `.env` file with your database credentials:
```bash
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=your_database
```

### 4. Run the Server
```bash
python server.py
```

## Installation

### System Requirements
- Python 3.11+ (as specified in pyproject.toml)
- MySQL 5.7+ (MySQL 8.0+ recommended)
- Minimum 512MB RAM for optimal connection pool performance
- FastMCP framework support

### Dependencies
Core dependencies:
- `fastmcp>=2.0.0`: Modern FastMCP framework
- `mysql-connector-python>=8.0.33`: Official MySQL connector with connection pooling
- `psutil>=5.9.0`: System monitoring for enhanced metrics (optional)

Development dependencies (optional):
- `pytest>=7.4.0`: Testing framework
- `pytest-asyncio>=0.21.0`: Async testing support
- `black>=23.0.0`: Code formatting
- `mypy>=1.5.0`: Static type checking
- `types-psutil>=5.9.0`: Type definitions for psutil

## Environment Configuration

### 🔗 Database Connection Configuration
| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| `MYSQL_HOST` | Database host address | localhost |
| `MYSQL_PORT` | Database port | 3306 |
| `MYSQL_USER` | Database username | root |
| `MYSQL_PASSWORD` | Database password | "" |
| `MYSQL_DATABASE` | Database name | Required |
| `MYSQL_CONNECTION_LIMIT` | Maximum connections in pool | 10 |
| `MYSQL_CONNECT_TIMEOUT` | Connection timeout (seconds) | 60 |
| `MYSQL_IDLE_TIMEOUT` | Idle connection timeout (seconds) | 60 |
| `MYSQL_SSL` | Enable SSL connection | false |

### 🛡️ Security Configuration
| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| `MAX_QUERY_LENGTH` | Maximum query length (characters) | 10000 |
| `ALLOWED_QUERY_TYPES` | Allowed query types (comma-separated) | SELECT,SHOW,DESCRIBE,INSERT,UPDATE,DELETE,CREATE,DROP,ALTER |
| `MAX_RESULT_ROWS` | Maximum result rows per query | 1000 |
| `QUERY_TIMEOUT` | Query execution timeout (seconds) | 30 |
| `RATE_LIMIT_WINDOW` | Rate limit time window (seconds) | 60 |
| `RATE_LIMIT_MAX` | Maximum requests per time window | 100 |

### ⚡ Performance Configuration
| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| `SCHEMA_CACHE_SIZE` | Table schema cache size | 128 |
| `TABLE_EXISTS_CACHE_SIZE` | Table existence cache size | 64 |
| `INDEX_CACHE_SIZE` | Index information cache size | 64 |
| `CACHE_TTL` | Cache expiration time (seconds) | 300 |
| `BATCH_SIZE` | Batch processing size | 1000 |
| `SLOW_QUERY_THRESHOLD` | Slow query threshold (seconds) | 1.0 |

## Claude Desktop Integration

To add this MySQL MCP server to your Claude Desktop configuration:

### 1. Locate Your Configuration File

The configuration file location depends on your operating system:

- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Linux**: `~/.config/Claude/claude_desktop_config.json`

### 2. Basic Configuration Example

```json
{
  "mcpServers": {
    "mysql-mcp-server": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_FastMCP/server.py"],
      "env": {
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "your_username",
        "MYSQL_PASSWORD": "your_password",
        "MYSQL_DATABASE": "your_database"
      }
    }
  }
}
```

### 3. High-Performance Production Configuration

```json
{
  "mcpServers": {
    "mysql-optimized": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_FastMCP/server.py"],
      "env": {
        "MYSQL_HOST": "prod-db-host",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "app_user",
        "MYSQL_PASSWORD": "secure_password",
        "MYSQL_DATABASE": "production_db",
        "MYSQL_CONNECTION_LIMIT": "20",
        "MAX_RESULT_ROWS": "2000",
        "RATE_LIMIT_MAX": "200",
        "SCHEMA_CACHE_SIZE": "256",
        "MYSQL_SSL": "true",
        "SLOW_QUERY_THRESHOLD": "0.5"
      }
    }
  }
}
```

### 4. Security-Restricted Configuration (Read-Only Permissions)

```json
{
  "mcpServers": {
    "mysql-readonly": {
      "command": "python",
      "args": ["/path/to/your/MySQL_MCP_FastMCP/server.py"],
      "env": {
        "MYSQL_HOST": "your-db-host",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "readonly_user",
        "MYSQL_PASSWORD": "secure_password",
        "MYSQL_DATABASE": "your_database",
        "MAX_RESULT_ROWS": "500",
        "RATE_LIMIT_MAX": "50",
        "ALLOWED_QUERY_TYPES": "SELECT,SHOW,DESCRIBE",
        "MYSQL_SSL": "true"
      }
    }
  }
}
```

## API Tools Reference

### 🔧 Core Data Operation Tools

| Tool Name | Description | Parameters | Caching | Performance Optimization |
|-----------|-------------|------------|---------|--------------------------|
| `mysql_query` | Execute custom SQL queries with prepared parameters | `query`: SQL string, `params`: Optional parameters | No | Batch processing, retry mechanism |
| `mysql_show_tables` | Show all database tables | None | No | Connection pool optimization |
| `mysql_describe_table` | Get detailed table structure | `table_name`: Table name | ✅ Yes | Smart caching, TTL management |
| `mysql_select_data` | Advanced data selection with conditions and limits | `table_name`, `columns`, `where_clause`, `limit` | No | Batch processing, result limiting |
| `mysql_insert_data` | Insert new data into table | `table_name`, `data`: Dictionary | No | Parameterized queries, input validation |
| `mysql_update_data` | Update existing table data | `table_name`, `data`, `where_clause` | No | Condition validation, security checks |
| `mysql_delete_data` | Delete data from table | `table_name`, `where_clause` | No | Condition validation, security checks |

### 📊 Schema Management Tools

| Tool Name | Description | Parameters | Caching | Special Features |
|-----------|-------------|------------|---------|------------------|
| `mysql_get_schema` | Get database schema information (tables, columns, constraints) | `table_name`: Optional | No | Supports single table or full database queries |
| `mysql_get_indexes` | Get table index information | `table_name`: Optional | ✅ Yes | Smart caching, performance statistics |
| `mysql_get_foreign_keys` | Get foreign key constraint information | `table_name`: Optional | No | Complete constraint relationships |
| `mysql_create_table` | Create new table with complete column definitions | `table_name`, `columns`: List | No | Automatic cache cleanup |
| `mysql_drop_table` | Drop table with IF EXISTS support | `table_name`, `if_exists`: Boolean | No | Automatic cache cleanup |

### 🔍 Monitoring and Diagnostic Tools

| Tool Name | Description | Parameters | Returns |
|-----------|-------------|------------|---------|
| `mysql_diagnose_connection` | Comprehensive system diagnostics including enhanced metrics | None | Connection pool status, configuration info, performance metrics, connection test, enhanced time-series metrics |

## Usage Examples

### Basic Database Operations

```python
# Show all tables
mysql_show_tables()

# Describe table structure (with caching for performance)
mysql_describe_table(table_name="users")

# Query data with conditions
mysql_select_data(
    table_name="users", 
    columns=["name", "email"], 
    where_clause="age > 18", 
    limit=10
)

# Execute custom queries
mysql_query(
    query="SELECT COUNT(*) FROM users WHERE status = %s",
    params=["active"]
)
```

### Data Modification Operations

```python
# Insert new record
mysql_insert_data(
    table_name="users",
    data={
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 25,
        "status": "active"
    }
)

# Update existing record
mysql_update_data(
    table_name="users",
    data={"age": 26, "status": "verified"},
    where_clause="email = '<EMAIL>'"
)

# Delete record
mysql_delete_data(
    table_name="users",
    where_clause="age < 18"
)
```

### Advanced Schema Management

```python
# Get comprehensive database schema information
mysql_get_schema()  # All tables
mysql_get_schema(table_name="users")  # Specific table

# Get index information (smart caching)
mysql_get_indexes(table_name="users")  # Specific table indexes
mysql_get_indexes()  # All table indexes

# Create new table (complete column definitions)
mysql_create_table(
    table_name="products",
    columns=[
        {
            "name": "id", 
            "type": "INT", 
            "primary_key": True, 
            "auto_increment": True,
            "nullable": False
        },
        {
            "name": "name", 
            "type": "VARCHAR(255)", 
            "nullable": False
        },
        {
            "name": "price", 
            "type": "DECIMAL(10,2)", 
            "nullable": False
        },
        {
            "name": "created_at", 
            "type": "TIMESTAMP", 
            "nullable": False,
            "default": "CURRENT_TIMESTAMP"
        }
    ]
)

# Safely drop table
mysql_drop_table(
    table_name="temp_table",
    if_exists=True  # Avoid table not exists errors
)
```

### Performance Monitoring and Diagnostics

```python
# Comprehensive system diagnostics with enhanced metrics
diagnosis = mysql_diagnose_connection()
print(f"Cache hit rate: {diagnosis['performance_metrics']['performance']['cache_hit_rate']:.2%}")
print(f"Average query time: {diagnosis['performance_metrics']['performance']['avg_query_time']:.3f}s")
print(f"Connection status: {diagnosis['connection_test']['status']}")
print(f"Available connections: {diagnosis['connection_pool_status']['available_connections']}")
print(f"Pool size: {diagnosis['connection_pool_status']['pool_size']}")
print(f"Health check active: {diagnosis['connection_pool_status']['health_check_active']}")

# Access enhanced time-series metrics
enhanced_metrics = diagnosis.get('enhanced_metrics', {})
if enhanced_metrics:
    print(f"Query performance stats: {enhanced_metrics.get('query_performance', {})}")
    print(f"Error statistics: {enhanced_metrics.get('error_statistics', {})}")
    print(f"Cache performance: {enhanced_metrics.get('cache_performance', {})}")
    print(f"System metrics: {enhanced_metrics.get('system_metrics', {})}")
```

## Architecture Design

### 🏗️ Core Component Architecture

```
MySQLManager
├── ConfigurationManager          # Centralized configuration management
├── ConnectionPool                # Enhanced connection pool management
├── SmartCache                    # Smart caching system
│   ├── SchemaCache              # Table structure cache
│   ├── TableExistsCache         # Table existence cache
│   └── IndexCache               # Index information cache
├── PerformanceMetrics           # Legacy performance metrics collector
├── EnhancedMetricsManager       # Time-series metrics with alerting
│   ├── TimeSeriesMetrics        # Individual metric collectors
│   └── AlertingSystem           # Configurable alert rules
├── EnhancedSecurityValidator    # Multi-level input validation
├── AdaptiveRateLimiter         # Token bucket rate limiting
└── RetryStrategy               # Exponential backoff retry logic
```

### 🔄 Data Flow Optimization
1. **Request Reception** → Rate limiting check → Input validation
2. **Cache Query** → Cache hit/miss statistics
3. **Connection Acquisition** → Connection pool health check → Query execution
4. **Result Processing** → Batch processing optimization → Performance metrics update
5. **Resource Cleanup** → Connection release → Cache management

### 📊 Constant-Based Design

The server adopts a zero magic number design with all configuration constants centrally defined:

```python
class DefaultConfig:
    MYSQL_PORT = 3306
    CONNECTION_LIMIT = 10
    MAX_QUERY_LENGTH = 10000
    MAX_RESULT_ROWS = 1000
    SCHEMA_CACHE_SIZE = 128
    CACHE_TTL = 300
    # ... more constants

class StringConstants:
    DEFAULT_HOST = "localhost"
    DEFAULT_USER = "root"
    CHARSET = "utf8mb4"
    SQL_MODE = "TRADITIONAL"
    # ... more string constants

class MySQLErrorCodes:
    ACCESS_DENIED = 1045
    DUPLICATE_ENTRY = 1062
    PARSE_ERROR = 1064
    # ... more error codes
```

### 🔧 Configuration Management System

The `ConfigurationManager` provides centralized, validated configuration:

```python
# Automatic validation and type checking
config_manager = ConfigurationManager()
database_config = config_manager.database  # DatabaseConfig instance
security_config = config_manager.security  # SecurityConfig instance
cache_config = config_manager.cache        # CacheConfig instance

# Configuration diagnosis
config_summary = config_manager.get_summary()
config_dict = config_manager.to_dict()  # Sensitive data masked
```

## Performance Monitoring

### 🎯 Key Performance Indicators

#### Query Performance Analysis
- **Average Query Time**: Mean execution time for all queries
- **Slow Query Identification**: Queries exceeding threshold automatically flagged
- **Error Rate Monitoring**: Query failure rate statistics and trend analysis
- **Concurrency Performance**: Multi-threaded query processing efficiency

#### Cache Efficiency Monitoring
```python
# Monitor cache performance
diagnosis = mysql_diagnose_connection()
cache_hit_rate = diagnosis['performance_metrics']['cache_stats']['schema_cache']['hit_rate']

# Cache hit rate > 80% indicates good performance
# Cache hit rate < 50% requires optimization
```

#### Connection Pool Health Status
- **Connection Utilization**: Active connection percentage
- **Wait Time Analysis**: Connection acquisition latency statistics
- **Connection Health Checks**: Automatic failed connection detection
- **Pool Scaling Recommendations**: Configuration suggestions based on usage patterns

### 📈 Performance Metrics Example

```json
{
  "performance": {
    "query_count": 1250,
    "avg_query_time": 0.045,
    "slow_query_count": 12,
    "error_rate": 0.008,
    "cache_hit_rate": 0.847
  },
  "cache_stats": {
    "schema_cache": {
      "size": 45,
      "max_size": 128,
      "hit_rate": 0.892,
      "ttl": 300
    }
  },
  "connection_pool": {
    "pool_size": 10,
    "available_connections": 7,
    "connection_stats": {
      "pool_hits": 890,
      "pool_waits": 23
    }
  }
}
```

## Caching Strategy

### 🧠 Multi-Level Smart Caching

#### Table Schema Cache (SchemaCache)
- **Cache Content**: Column definitions, data types, constraints, comments
- **TTL**: 5 minutes (configurable)
- **Invalidation Strategy**: Automatic cleanup after DDL operations
- **Statistics**: Hit rate, access frequency

#### Table Existence Cache (ExistsCache)
- **Cache Content**: Boolean values indicating table existence
- **Use Cases**: Frequent table validation operations
- **Invalidation Timing**: CREATE/DROP TABLE operations

#### Index Information Cache (IndexCache)
- **Cache Content**: Index names, columns, types, uniqueness
- **Performance Impact**: Significantly reduces INFORMATION_SCHEMA queries
- **Smart Updates**: Automatic refresh after ALTER TABLE operations

### Cache Management Best Practices

```python
# Cache tuning recommendations
SCHEMA_CACHE_SIZE = 128      # Suitable for medium-scale applications
TABLE_EXISTS_CACHE_SIZE = 64 # Fast existence checking
INDEX_CACHE_SIZE = 64        # Index query optimization
CACHE_TTL = 300              # 5-minute expiration balances performance and consistency
```

## Security Features

### 🔒 Multi-Layer Security Protection System

#### Input Validation and Sanitization
- **Null Byte Filtering**: Prevent string truncation attacks
- **Length Limiting**: Prevent buffer overflow
- **Dangerous Pattern Detection**: Identify potential SQL injection attempts
- **Character Encoding Validation**: Ensure UTF-8 compatibility

#### MySQL Error Smart Classification
```python
class MySQLErrorCodes:
    ACCESS_DENIED = 1045           # Access permission errors
    UNKNOWN_DATABASE = 1049        # Database doesn't exist
    DUPLICATE_ENTRY = 1062         # Duplicate entry errors
    PARSE_ERROR = 1064             # SQL syntax errors
    CANT_CONNECT_TO_SERVER = 2003  # Connection errors
```

#### Rate Limiting Algorithm
- **Sliding Window**: 60-second time window
- **Rate Control**: Default 100 requests/minute
- **Protection Features**: Prevent brute force attacks and resource abuse
- **Configurable Thresholds**: Adjust limits based on requirements

### 🛡️ Security Monitoring and Auditing
- **Operation Logging**: Complete recording of all SQL operations
- **Security Events**: Automatic flagging of abnormal operations
- **Credential Protection**: Automatic masking of sensitive information in logs
- **Connection Auditing**: Tracking of connection establishment and disconnection

## Troubleshooting

### 🔧 Common Issue Solutions

#### 1. Database Connection Failed
```
Error: MySQL connection error: Can't connect to MySQL server
```
**Solutions**:
- Verify MySQL service is running: `systemctl status mysql`
- Check connection parameters in environment variables
- Test connectivity: `telnet <host> <port>`
- Use `mysql_diagnose_connection()` for detailed analysis

#### 2. Low Cache Hit Rate
```bash
# Symptom: Frequent INFORMATION_SCHEMA queries
# Solution:
export SCHEMA_CACHE_SIZE=256
export CACHE_TTL=600
# Restart server to apply new configuration
```

#### 3. Connection Pool Exhaustion
```bash
# Symptom: Connection pool exhausted
# Diagnosis: Use mysql_diagnose_connection for checking
# Solution:
export MYSQL_CONNECTION_LIMIT=20
export MYSQL_CONNECT_TIMEOUT=120
```

#### 4. Rate Limiting Triggered
```
Error: Rate limit exceeded. Please try again later.
```
**Solutions**:
- Reduce request frequency
- Increase `RATE_LIMIT_MAX` setting
- Extend `RATE_LIMIT_WINDOW` duration

### 🔍 Advanced Diagnostic Tools

```python
# Use comprehensive diagnostics for system analysis
diagnosis = mysql_diagnose_connection()

# Check key performance indicators
performance = diagnosis['performance_metrics']['performance']
print(f"Query error rate: {performance['error_rate']:.3f}")
print(f"Cache hit rate: {performance['cache_hit_rate']:.3f}")
print(f"Average query time: {performance['avg_query_time']:.3f}s")

# Connection pool health check
pool_stats = diagnosis['connection_pool_status']
print(f"Available connections: {pool_stats['available_connections']}")
if 'connection_stats' in pool_stats:
    print(f"Pool waits: {pool_stats['connection_stats']['pool_waits']}")
```

## Performance Tuning

### ⚡ Configuration Templates

#### Enterprise Environment
```bash
# High-concurrency, large dataset configuration
export MYSQL_CONNECTION_LIMIT=50
export MAX_RESULT_ROWS=5000
export RATE_LIMIT_MAX=500
export SCHEMA_CACHE_SIZE=512
export TABLE_EXISTS_CACHE_SIZE=256
export INDEX_CACHE_SIZE=256
export BATCH_SIZE=2000
export SLOW_QUERY_THRESHOLD=0.3
export CACHE_TTL=600
```

#### Medium-Scale Applications
```bash
# Balanced performance and resource consumption
export MYSQL_CONNECTION_LIMIT=20
export MAX_RESULT_ROWS=2000
export RATE_LIMIT_MAX=200
export SCHEMA_CACHE_SIZE=128
export TABLE_EXISTS_CACHE_SIZE=64
export INDEX_CACHE_SIZE=64
export BATCH_SIZE=1000
export SLOW_QUERY_THRESHOLD=1.0
export CACHE_TTL=300
```

#### Resource-Constrained Environment
```bash
# Low memory, low CPU configuration
export MYSQL_CONNECTION_LIMIT=5
export MAX_RESULT_ROWS=500
export RATE_LIMIT_MAX=50
export SCHEMA_CACHE_SIZE=32
export TABLE_EXISTS_CACHE_SIZE=16
export INDEX_CACHE_SIZE=16
export BATCH_SIZE=100
export SLOW_QUERY_THRESHOLD=2.0
export CACHE_TTL=180
```

### 📈 Performance Benchmarks

| Metric | Target | Warning Threshold |
|--------|--------|-------------------|
| Cache Hit Rate | > 80% | < 60% |
| Average Query Time | < 100ms | > 500ms |
| Error Rate | < 1% | > 5% |
| Connection Pool Wait Rate | < 10% | > 30% |
| Slow Query Rate | < 5% | > 15% |

## Development Guide

### 🏗️ Architecture Principles
1. **Performance First**: All design decisions prioritize performance impact
2. **Security First**: Multi-layer security validation, never trust user input
3. **Observability**: Comprehensive monitoring and diagnostic capabilities
4. **Configuration-Driven**: Flexible configuration through environment variables
5. **Graceful Degradation**: Cache failures don't affect core functionality

### 🔧 Extension Development

```python
# Add new cache types
class CustomCache(SmartCache):
    def __init__(self, max_size: int):
        super().__init__(max_size)
        # Custom caching logic

# Extend performance metrics
@dataclass
class ExtendedMetrics(PerformanceMetrics):
    custom_metric: int = 0
    
    def update_custom_metric(self, value: int):
        self.custom_metric += value
```

### Contributing Guidelines

We welcome contributions to improve the MySQL MCP FastMCP server!

#### Development Environment Setup
```bash
# Clone repository
git clone <repository-url>
cd MySQL_MCP_FastMCP

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/

# Code formatting
black server.py

# Type checking
mypy server.py
```

#### Contribution Guidelines
- Follow PEP 8 style guidelines
- Add tests for new features
- Update documentation for changes
- Ensure all constants are properly defined
- Include security considerations in new features

## License

MIT License

---

## Architecture Decision Records

### 🏗️ Core Design Principles
1. **Performance First**: All design decisions prioritize performance impact
2. **Security First**: Multi-layer security validation, never trust user input
3. **Observability**: Comprehensive monitoring and diagnostic capabilities
4. **Configuration-Driven**: Flexible configuration through environment variables
5. **Graceful Degradation**: Cache failures don't affect core functionality

### Technology Choices
- **FastMCP Framework**: Chosen for modern async support and performance
- **LRU Caching**: Implements intelligent metadata caching with TTL
- **Constant-Based Configuration**: Eliminates magic numbers and strings
- **Comprehensive Error Handling**: Provides actionable error messages
- **Security-First Design**: Multiple layers of protection
- **MySQL-Specific Optimizations**: Tailored for MySQL performance characteristics

---

**Built with ⚡ using FastMCP framework for enterprise-grade applications providing superior performance and reliability**